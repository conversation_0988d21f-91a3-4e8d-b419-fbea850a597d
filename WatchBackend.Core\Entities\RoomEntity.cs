﻿namespace WatchBackend.Core.Entities
{
    public class RoomEntity : BaseAuditableEntity
    {
        public string IdHash { get; set; } = string.Empty;
        public List<RoomUserEntity> Users { get; set; } = new List<RoomUserEntity>();
        public PlaybackState PlaybackState { get; set; }
        public TimeSpan CurrentTime { get; set; }
        public SourceContainerPlaylistEntity Playlist { get; set; } = new();
    }

    public enum PlaybackState
    {
        Stopped = 0,
        Paused,
        Playing
    }
}
