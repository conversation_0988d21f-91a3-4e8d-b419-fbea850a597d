﻿using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WatchBackend.Core.Interfaces;
using WatchBackend.Data.Interceptors;
using WatchBackend.Data.Repositories;

namespace WatchBackend.Data
{
    public static class ConfigureServices
    {
        public static IServiceCollection AddDataServices(
            this IServiceCollection services,
            IConfiguration configuration
        )
        {
            if (configuration.GetValue<bool>("UseInMemoryDatabase"))
            {
                services.AddDbContext<WatchBackendDbContext>(
                    options => options.UseInMemoryDatabase("WatchBackendDb")
                );
            }
            else
            {
                services.AddDbContext<WatchBackendDbContext>(options =>
                {
                    options.UseSqlite(
                        configuration.GetConnectionString("SqliteConnection"),
                        builder =>
                            builder.MigrationsAssembly(
                                typeof(WatchBackendDbContext).Assembly.FullName
                            )
                    );
                    options.EnableSensitiveDataLogging();
                });
            }

            services.AddScoped<IRoomRepository, RoomRepository>();
            services.AddScoped<IRoomUserRepository, RoomUserRepository>();
            services.AddScoped<
                ISourceContainerPlaylistRepository,
                SourceContainerPlaylistRepository
            >();
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<AuditableEntitySaveChangesInterceptor>();

            TypeAdapterConfig.GlobalSettings.Scan(typeof(ConfigureServices).Assembly);

            return services;
        }
    }
}
