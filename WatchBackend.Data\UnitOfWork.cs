﻿using WatchBackend.Core.Interfaces;

namespace WatchBackend.Data
{
    internal class UnitOfWork : IUnitOfWork
    {
        private readonly WatchBackendDbContext _context;

        public IRoomRepository Rooms { get; init; }
        public IRoomUserRepository RoomUsers { get; init; }

        public UnitOfWork(
            WatchBackendDbContext context,
            IRoomRepository roomRepository,
            IRoomUserRepository roomsUsers
        )
        {
            _context = context;
            Rooms = roomRepository;
            RoomUsers = roomsUsers;
        }

        public Task CompleteAsync()
        {
            return _context.SaveChangesAsync();
        }
    }
}
