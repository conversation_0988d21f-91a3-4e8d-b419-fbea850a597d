﻿namespace WatchBackend.Core.Entities
{
    public class SourceContainerEntity : BaseAuditableEntity
    {
        public string Type { get; init; } = string.Empty;
        public VideoSourceEntity? Video { get; init; }
        public ICollection<SubtitleSourceEntity>? Subtitles { get; init; }
        public IDictionary<string, string>? Attributes { get; init; }
        public BaseExternalInfoEntity? ExternalInfo { get; init; }
    }

    public class VideoSourceEntity : BaseAuditableEntity
    {
        public string Url { get; set; } = string.Empty;
        public IDictionary<string, string>? Attributes { get; init; }
        public int? Width { get; init; }
        public int? Height { get; init; }
    }

    public class SubtitleSourceEntity : BaseAuditableEntity
    {
        public string Url { get; init; } = string.Empty;
        public string Label { get; init; } = string.Empty;
        public string SrcLang { get; init; } = string.Empty;
        public double Offset { get; set; } = 0;
        public IDictionary<string, string>? Attributes { get; init; }
    }

    public class BaseExternalInfoEntity : BaseAuditableEntity
    {
        public string? Title { get; set; }
    }

    public class BaseMovieAndSeriesInfoEntity : BaseExternalInfoEntity
    {
        public string? OriginalTitle { get; set; }
        public string? Description { get; set; }
        public DateOnly? ReleaseDate { get; set; }
        public string? PosterImageUrl { get; set; }
        public string? BackdropImageUrl { get; set; }
        public string? BackdropPlaceholderUrl { get; set; }
        public string? ImdbId { get; set; }
        public ICollection<string>? OriginCountries { get; set; }
        public ICollection<string>? Genres { get; set; }
        public ICollection<string>? SpokenLanguages { get; set; }
        public ICollection<CompanyEntity>? ProductionCompanies { get; set; }
    }

    public class BaseMovieAndSeriesCreditsEntity : BaseAuditableEntity
    {
        public ICollection<ActorEntity>? Actors { get; set; }
        public ICollection<CrewEntity>? Writers { get; set; }
        public ICollection<CrewEntity>? Directors { get; set; }
        public ICollection<CrewEntity>? OriginalMusicComposers { get; set; }
    }

    public class SeriesInfoEntity : BaseMovieAndSeriesInfoEntity
    {
        public int? SeasonNumber { get; set; }
        public int? EpisodeNumber { get; set; }
        public string? EpisodeTitle { get; set; }
        public ICollection<CompanyEntity>? Networks { get; set; }
        public SeriesCreditsEntity? Credits { get; set; }
    }

    public class SeriesCreditsEntity : BaseMovieAndSeriesCreditsEntity
    {
        public ICollection<ActorEntity>? GuestActors { get; set; }
        public ICollection<CrewEntity>? Creators { get; set; }
    }

    public class MovieInfoEntity : BaseMovieAndSeriesInfoEntity
    {
        public string? Tagline { get; set; }
        public uint? Budget { get; set; }
        public MovieCreditsEntity? Credits { get; set; }
    }

    public class MovieCreditsEntity : BaseMovieAndSeriesCreditsEntity { }

    public class CompanyEntity : BaseAuditableEntity
    {
        public string? Name { get; set; }
        public string? LogoImageUrl { get; set; }
    }

    public class CrewEntity : BaseAuditableEntity
    {
        public string? Name { get; set; }
        public string? ProfileImageUrl { get; set; }
    }

    public class ActorEntity : CrewEntity
    {
        public string? Character { get; set; }
        public int? Order { get; set; }
    }
}
