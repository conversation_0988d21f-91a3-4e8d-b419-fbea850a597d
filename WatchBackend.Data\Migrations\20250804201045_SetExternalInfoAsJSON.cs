using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WatchBackend.Data.Migrations
{
    /// <inheritdoc />
    public partial class SetExternalInfoAsJSON : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SourceContainerEntity_BaseExternalInfoEntity_ExternalInfoId",
                table: "SourceContainerEntity");

            migrationBuilder.DropTable(
                name: "CompanyEntity");

            migrationBuilder.DropTable(
                name: "CrewEntity");

            migrationBuilder.DropTable(
                name: "BaseExternalInfoEntity");

            migrationBuilder.DropTable(
                name: "MovieCreditsEntity");

            migrationBuilder.DropTable(
                name: "SeriesCreditsEntity");

            migrationBuilder.DropIndex(
                name: "IX_SourceContainerEntity_ExternalInfoId",
                table: "SourceContainerEntity");

            migrationBuilder.DropColumn(
                name: "ExternalInfoId",
                table: "SourceContainerEntity");

            migrationBuilder.AddColumn<string>(
                name: "ExternalInfo",
                table: "SourceContainerEntity",
                type: "TEXT",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExternalInfo",
                table: "SourceContainerEntity");

            migrationBuilder.AddColumn<int>(
                name: "ExternalInfoId",
                table: "SourceContainerEntity",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "MovieCreditsEntity",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Created = table.Column<DateTime>(type: "TEXT", nullable: false),
                    LastModified = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MovieCreditsEntity", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SeriesCreditsEntity",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Created = table.Column<DateTime>(type: "TEXT", nullable: false),
                    LastModified = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SeriesCreditsEntity", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BaseExternalInfoEntity",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    external_info_type = table.Column<string>(name: "$external_info_type", type: "TEXT", nullable: false),
                    Created = table.Column<DateTime>(type: "TEXT", nullable: false),
                    LastModified = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Title = table.Column<string>(type: "TEXT", nullable: true),
                    BackdropImageUrl = table.Column<string>(type: "TEXT", nullable: true),
                    BackdropPlaceholderUrl = table.Column<string>(type: "TEXT", nullable: true),
                    Description = table.Column<string>(type: "TEXT", nullable: true),
                    Genres = table.Column<string>(type: "TEXT", nullable: true),
                    ImdbId = table.Column<string>(type: "TEXT", nullable: true),
                    OriginCountries = table.Column<string>(type: "TEXT", nullable: true),
                    OriginalTitle = table.Column<string>(type: "TEXT", nullable: true),
                    PosterImageUrl = table.Column<string>(type: "TEXT", nullable: true),
                    ReleaseDate = table.Column<DateOnly>(type: "TEXT", nullable: true),
                    SpokenLanguages = table.Column<string>(type: "TEXT", nullable: true),
                    CreditsId = table.Column<int>(type: "INTEGER", nullable: true),
                    Budget = table.Column<uint>(type: "INTEGER", nullable: true),
                    Tagline = table.Column<string>(type: "TEXT", nullable: true),
                    SeriesInfoEntity_CreditsId = table.Column<int>(type: "INTEGER", nullable: true),
                    EpisodeNumber = table.Column<int>(type: "INTEGER", nullable: true),
                    EpisodeTitle = table.Column<string>(type: "TEXT", nullable: true),
                    SeasonNumber = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BaseExternalInfoEntity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BaseExternalInfoEntity_MovieCreditsEntity_CreditsId",
                        column: x => x.CreditsId,
                        principalTable: "MovieCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BaseExternalInfoEntity_SeriesCreditsEntity_SeriesInfoEntity_CreditsId",
                        column: x => x.SeriesInfoEntity_CreditsId,
                        principalTable: "SeriesCreditsEntity",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CrewEntity",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Created = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Discriminator = table.Column<string>(type: "TEXT", nullable: false),
                    LastModified = table.Column<DateTime>(type: "TEXT", nullable: false),
                    MovieCreditsEntityId = table.Column<int>(type: "INTEGER", nullable: true),
                    MovieCreditsEntityId1 = table.Column<int>(type: "INTEGER", nullable: true),
                    MovieCreditsEntityId2 = table.Column<int>(type: "INTEGER", nullable: true),
                    Name = table.Column<string>(type: "TEXT", nullable: true),
                    ProfileImageUrl = table.Column<string>(type: "TEXT", nullable: true),
                    SeriesCreditsEntityId2 = table.Column<int>(type: "INTEGER", nullable: true),
                    SeriesCreditsEntityId3 = table.Column<int>(type: "INTEGER", nullable: true),
                    SeriesCreditsEntityId4 = table.Column<int>(type: "INTEGER", nullable: true),
                    SeriesCreditsEntityId5 = table.Column<int>(type: "INTEGER", nullable: true),
                    Character = table.Column<string>(type: "TEXT", nullable: true),
                    MovieCreditsEntityId3 = table.Column<int>(type: "INTEGER", nullable: true),
                    Order = table.Column<int>(type: "INTEGER", nullable: true),
                    SeriesCreditsEntityId = table.Column<int>(type: "INTEGER", nullable: true),
                    SeriesCreditsEntityId1 = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CrewEntity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CrewEntity_MovieCreditsEntity_MovieCreditsEntityId",
                        column: x => x.MovieCreditsEntityId,
                        principalTable: "MovieCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_MovieCreditsEntity_MovieCreditsEntityId1",
                        column: x => x.MovieCreditsEntityId1,
                        principalTable: "MovieCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_MovieCreditsEntity_MovieCreditsEntityId2",
                        column: x => x.MovieCreditsEntityId2,
                        principalTable: "MovieCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_MovieCreditsEntity_MovieCreditsEntityId3",
                        column: x => x.MovieCreditsEntityId3,
                        principalTable: "MovieCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_SeriesCreditsEntity_SeriesCreditsEntityId",
                        column: x => x.SeriesCreditsEntityId,
                        principalTable: "SeriesCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_SeriesCreditsEntity_SeriesCreditsEntityId1",
                        column: x => x.SeriesCreditsEntityId1,
                        principalTable: "SeriesCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_SeriesCreditsEntity_SeriesCreditsEntityId2",
                        column: x => x.SeriesCreditsEntityId2,
                        principalTable: "SeriesCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_SeriesCreditsEntity_SeriesCreditsEntityId3",
                        column: x => x.SeriesCreditsEntityId3,
                        principalTable: "SeriesCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_SeriesCreditsEntity_SeriesCreditsEntityId4",
                        column: x => x.SeriesCreditsEntityId4,
                        principalTable: "SeriesCreditsEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CrewEntity_SeriesCreditsEntity_SeriesCreditsEntityId5",
                        column: x => x.SeriesCreditsEntityId5,
                        principalTable: "SeriesCreditsEntity",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CompanyEntity",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    BaseMovieAndSeriesInfoEntityId = table.Column<int>(type: "INTEGER", nullable: true),
                    Created = table.Column<DateTime>(type: "TEXT", nullable: false),
                    LastModified = table.Column<DateTime>(type: "TEXT", nullable: false),
                    LogoImageUrl = table.Column<string>(type: "TEXT", nullable: true),
                    Name = table.Column<string>(type: "TEXT", nullable: true),
                    SeriesInfoEntityId = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyEntity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyEntity_BaseExternalInfoEntity_BaseMovieAndSeriesInfoEntityId",
                        column: x => x.BaseMovieAndSeriesInfoEntityId,
                        principalTable: "BaseExternalInfoEntity",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CompanyEntity_BaseExternalInfoEntity_SeriesInfoEntityId",
                        column: x => x.SeriesInfoEntityId,
                        principalTable: "BaseExternalInfoEntity",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_SourceContainerEntity_ExternalInfoId",
                table: "SourceContainerEntity",
                column: "ExternalInfoId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseExternalInfoEntity_CreditsId",
                table: "BaseExternalInfoEntity",
                column: "CreditsId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseExternalInfoEntity_SeriesInfoEntity_CreditsId",
                table: "BaseExternalInfoEntity",
                column: "SeriesInfoEntity_CreditsId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyEntity_BaseMovieAndSeriesInfoEntityId",
                table: "CompanyEntity",
                column: "BaseMovieAndSeriesInfoEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyEntity_SeriesInfoEntityId",
                table: "CompanyEntity",
                column: "SeriesInfoEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_MovieCreditsEntityId",
                table: "CrewEntity",
                column: "MovieCreditsEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_MovieCreditsEntityId1",
                table: "CrewEntity",
                column: "MovieCreditsEntityId1");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_MovieCreditsEntityId2",
                table: "CrewEntity",
                column: "MovieCreditsEntityId2");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_MovieCreditsEntityId3",
                table: "CrewEntity",
                column: "MovieCreditsEntityId3");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_SeriesCreditsEntityId",
                table: "CrewEntity",
                column: "SeriesCreditsEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_SeriesCreditsEntityId1",
                table: "CrewEntity",
                column: "SeriesCreditsEntityId1");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_SeriesCreditsEntityId2",
                table: "CrewEntity",
                column: "SeriesCreditsEntityId2");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_SeriesCreditsEntityId3",
                table: "CrewEntity",
                column: "SeriesCreditsEntityId3");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_SeriesCreditsEntityId4",
                table: "CrewEntity",
                column: "SeriesCreditsEntityId4");

            migrationBuilder.CreateIndex(
                name: "IX_CrewEntity_SeriesCreditsEntityId5",
                table: "CrewEntity",
                column: "SeriesCreditsEntityId5");

            migrationBuilder.AddForeignKey(
                name: "FK_SourceContainerEntity_BaseExternalInfoEntity_ExternalInfoId",
                table: "SourceContainerEntity",
                column: "ExternalInfoId",
                principalTable: "BaseExternalInfoEntity",
                principalColumn: "Id");
        }
    }
}
