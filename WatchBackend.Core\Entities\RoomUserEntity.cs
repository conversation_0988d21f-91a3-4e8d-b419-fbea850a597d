﻿namespace WatchBackend.Core.Entities
{
    public class RoomUserEntity : BaseAuditableEntity
    {
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public RoomUserEntityRoles Role { get; set; }
    }

    public enum RoomUserEntityRoles
    {
        Guest = 0,
        Viewer,
        Moderator,
        Administrator,
        Creator,
    }
}
