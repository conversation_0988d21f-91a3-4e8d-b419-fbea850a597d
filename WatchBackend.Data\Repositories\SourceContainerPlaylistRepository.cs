﻿using WatchBackend.Core.Entities;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Data.Repositories
{
    internal class SourceContainerPlaylistRepository : ISourceContainerPlaylistRepository
    {
        private readonly WatchBackendDbContext _context;

        public SourceContainerPlaylistRepository(WatchBackendDbContext context)
        {
            _context = context;
        }

        public void Add(SourceContainerPlaylistEntity playlistEntity)
        {
            _context.SourceContainerPlaylists.Add(playlistEntity);
        }

        public void Update(SourceContainerPlaylistEntity playlistEntity)
        {
            _context.SourceContainerPlaylists.Update(playlistEntity);
        }
    }
}
