﻿using System.Net;
using System.Net.Mail;
using System.Net.WebSockets;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using FluentEmail.Core;
using Mapster;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using NUglify.Helpers;
using Polly;
using WatchBackend.Core.Extensions;
using WatchBackend.Core.Interfaces;
using WatchBackend.Data;
using WatchBackend.Data.Models;
using WatchBackend.Infrastructure.Cache;
using WatchBackend.Infrastructure.Extensions;
using WatchBackend.Infrastructure.Interfaces;
using WatchBackend.Infrastructure.Models;
using WatchBackend.Infrastructure.Providers;
using WatchBackend.Infrastructure.Services;

namespace WatchBackend.Infrastructure
{
    public static class ConfigureServices
    {
        public static IServiceCollection AddInfrastructureServices(
            this IServiceCollection services,
            IConfiguration configuration
        )
        {
            var jwtOptions = configuration.GetSection("JwtOptions");
            var jwtSecretKey = jwtOptions["Secret"];
            var confirmationTokenProviderOptions = configuration.GetSection(
                "ConfirmationTokenProviderOptions"
            );
            var confirmationTokenProviderName = confirmationTokenProviderOptions["Name"];
            var recoveryTokenProviderOptions = configuration.GetSection(
                "RecoveryTokenProviderOptions"
            );
            var recoveryTokenProviderName = recoveryTokenProviderOptions["Name"];

            var builder = services
                //.AddIdentityCore<ApplicationUser>()
                .AddIdentity<ApplicationUser, ApplicationRole>()
                .AddRoles<ApplicationRole>()
                .AddDefaultTokenProviders()
                .AddEntityFrameworkStores<WatchBackendDbContext>();

            var userType = builder.UserType;
            var confirmationTokenProvider = typeof(ConfirmationTokenProvider<>).MakeGenericType(
                userType
            );
            var recoveryTokenProvider = typeof(RecoveryTokenProvider<>).MakeGenericType(userType);

            builder.AddTokenProvider(confirmationTokenProviderName, confirmationTokenProvider);
            builder.AddTokenProvider(recoveryTokenProviderName, recoveryTokenProvider);

            services
                .AddAuthentication(options =>
                {
                    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                })
                .AddJwtBearer(options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = jwtOptions["ValidIssuer"],
                        ValidAudience = jwtOptions["ValidAudience"],
                        IssuerSigningKey = new SymmetricSecurityKey(
                            Encoding.UTF8.GetBytes(jwtSecretKey)
                        ),
                    };

                    options.Events = new JwtBearerEvents
                    {
                        OnMessageReceived = context =>
                        {
                            var accessToken = context.Request.Query["access_token"];
                            var path = context.HttpContext.Request.Path;
                            if (
                                !string.IsNullOrEmpty(accessToken)
                                && path.StartsWithSegments("/roomHub")
                            )
                            {
                                context.Token = accessToken;
                            }

                            return Task.CompletedTask;
                        },
                        OnTokenValidated = async context =>
                        {
                            var path = context.HttpContext.Request.Path;
                            if (path.StartsWithSegments("/roomHub"))
                            {
                                var user = context.Principal;
                                if (user == null)
                                {
                                    context.Fail("No user provided");
                                    return;
                                }

                                var userIdResult = user.GetClaimAsInt(ClaimTypes.NameIdentifier);
                                if (userIdResult.IsFailed)
                                {
                                    context.Fail("No name identifier provided");
                                    return;
                                }

                                var userId = userIdResult.Value;
                                var userService =
                                    context.HttpContext.RequestServices.GetRequiredService<IUserService>();
                                var userResult = await userService.GetUserById(userId);
                                if (userResult.IsFailed)
                                {
                                    context.Fail("Invalid user");
                                }
                            }
                        },
                    };
                });

            var emailOptions = configuration.GetSection("EmailOptions");
            var fromAddress = emailOptions["FromAddress"];
            var fromName = emailOptions["FromName"];
            var mailtrapUsername = emailOptions["MailtrapUsername"];
            var mailtrapPassword = emailOptions["MailtrapPassword"];
            var mailtrapHost = emailOptions["MailtrapHost"];
            var mailtrapPort = int.Parse(emailOptions["MailtrapPort"]);
            services
                .AddFluentEmail(fromAddress, fromName)
                .AddPassthroughRenderer()
                .AddMailtrapSender(mailtrapUsername, mailtrapPassword, mailtrapHost, mailtrapPort);

            EmbeddedTemplates.Configure(
                Assembly.GetExecutingAssembly(),
                "WatchBackend.Infrastructure.Templates.Email"
            );

            services.Configure<HashIdsOptions>(configuration.GetSection("HashIdsOptions"));
            services.Configure<JwtOptions>(jwtOptions);
            services.Configure<EmailOptions>(configuration.GetSection("EmailOptions"));
            services.Configure<ConfirmationTokenProviderOptions>(confirmationTokenProviderOptions);
            services.Configure<RecoveryTokenProviderOptions>(recoveryTokenProviderOptions);

            services
                .AddHttpClient("VideoService")
                .AddTransientHttpErrorPolicy(p =>
                    p.WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(300))
                );

            services.AddScoped<IRoomService, RoomService>();
            services.AddSingleton<IIdHashService, IdHashService>();
            services.AddScoped<IUserService, UserService>();
            services.AddSingleton<IUsernameService, UsernameService>();
            services.AddScoped<ISourceService, SourceService>();
            services.AddScoped<IEmailService, FluentEmailService>();

            services.AddSingleton<IRoomCache, RoomCache>();

            TypeAdapterConfig.GlobalSettings.Scan(typeof(ConfigureServices).Assembly);

            return services;
        }
    }
}
