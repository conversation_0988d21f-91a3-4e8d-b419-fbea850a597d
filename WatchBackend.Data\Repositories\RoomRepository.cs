﻿using Microsoft.EntityFrameworkCore;
using WatchBackend.Core.Entities;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Queries;

namespace WatchBackend.Data.Repositories
{
    internal class RoomRepository : IRoomRepository
    {
        private readonly WatchBackendDbContext _context;

        public RoomRepository(WatchBackendDbContext context)
        {
            _context = context;
        }

        public void Add(RoomEntity roomEntity)
        {
            _context.Rooms.Add(roomEntity);
        }

        public void Update(RoomEntity roomEntity)
        {
            _context.Rooms.Update(roomEntity);
        }

        public Task<RoomEntity?> GetByIdAsync(int id, RoomQuery? roomQuery = null)
        {
            var rooms = _context.Rooms.AsQueryable();
            if (roomQuery is not null)
            {
                rooms = rooms.ApplyIncludes(roomQuery);
            }

            return rooms.FirstOrDefaultAsync(r => r.Id == id);
        }
    }

    internal static class QueryableRoomEntityExtensions
    {
        public static IQueryable<RoomEntity> ApplyIncludes(
            this IQueryable<RoomEntity> rooms,
            RoomQuery roomQuery
        )
        {
            if (roomQuery.IncludeRoomUsers)
            {
                rooms = rooms.Include(r => r.Users);
            }

            if (roomQuery.IncludeRoomPlaylist)
            {
                rooms = rooms
                    .Include("Playlist.Items.Video")
                    .Include("Playlist.Items.Subtitles")
                    .Include("Playlist.Items.ExternalInfo");
                //.Include("Playlist.Items.ExternalInfo.ProductionCompanies")
                //.Include("Playlist.Items.ExternalInfo.Networks")
                //.Include("Playlist.Items.ExternalInfo.Credits")
                //.Include("Playlist.Items.ExternalInfo.Credits.Actors")
                //.Include("Playlist.Items.ExternalInfo.Credits.GuestActors")
                //.Include("Playlist.Items.ExternalInfo.Credits.Creators")
                //.Include("Playlist.Items.ExternalInfo.Credits.Writers")
                //.Include("Playlist.Items.ExternalInfo.Credits.Directors")
                //.Include("Playlist.Items.ExternalInfo.Credits.OriginalMusicComposers");
            }

            return rooms;
        }
    }
}
